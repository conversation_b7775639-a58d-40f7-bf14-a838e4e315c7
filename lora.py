import torch
import torch.nn as nn

# 手写LoRA线性层
class LoRALinear(nn.Module):
    def __init__(self, in_features, out_features, r=4, lora_alpha=8, lora_dropout=0.1):
        super().__init__()
        self.linear = nn.Linear(in_features, out_features)
        self.r = r
        self.lora_alpha = lora_alpha
        self.scaling = lora_alpha / r
        self.lora_A = nn.Parameter(torch.randn(r, in_features) * 0.01)
        self.lora_B = nn.Parameter(torch.randn(out_features, r) * 0.01)
        self.lora_dropout = nn.Dropout(lora_dropout) if lora_dropout > 0.0 else nn.Identity()
        # 冻结主权重
        for param in self.linear.parameters():
            param.requires_grad = False

    def forward(self, x):
        result = self.linear(x)
        lora_part = (self.lora_dropout(x) @ self.lora_A.t()) @ self.lora_B.t() * self.scaling
        return result + lora_part

# 1. 定义一个简单的基础模型 (一个线性层)
class SimpleModel(nn.Module):
    def __init__(self, input_dim=10, output_dim=5, r=4, lora_alpha=8, lora_dropout=0.1):
        super().__init__()
        self.linear = LoRALinear(input_dim, output_dim, r, lora_alpha, lora_dropout)

    def forward(self, x):
        return self.linear(x)

# 创建基础模型实例（已集成LoRA）
model_with_lora = SimpleModel(input_dim=10, output_dim=5, r=4, lora_alpha=8, lora_dropout=0.1)
print("--- Base Model Structure (with LoRA) ---")
print(model_with_lora)
print("\n--- Model Parameters (requires_grad) ---")
for name, param in model_with_lora.named_parameters():
    print(f"{name}: {param.requires_grad}")

# 打印原始权重 (为了后续比较)
original_weight_before_lora = model_with_lora.linear.linear.weight.data.clone()
print("\nOriginal Linear Weight (before LoRA):")
print(original_weight_before_lora)

# 4. 查看参数状态 (哪些是可训练的)
print("\n--- Parameters After Applying LoRA (requires_grad) ---")
total_params = 0
trainable_params = 0
for name, param in model_with_lora.named_parameters():
    total_params += param.numel()
    if param.requires_grad:
        trainable_params += param.numel()
        print(f"{name}: {param.requires_grad} (Trainable)")
    else:
        print(f"{name}: {param.requires_grad} (Frozen)")

print(f"\nTotal parameters: {total_params}")
print(f"Trainable parameters (LoRA): {trainable_params}")
print(f"Trainable percentage: {100 * trainable_params / total_params:.2f}%")

# 打印应用 LoRA 后的权重 (原始权重应该没变，但多了 lora_A 和 lora_B)
original_weight_after_lora = model_with_lora.linear.linear.weight.data
lora_A_weight = model_with_lora.linear.lora_A.data
lora_B_weight = model_with_lora.linear.lora_B.data

print("\nOriginal Linear Weight (after LoRA applied, should be unchanged):")
print(original_weight_after_lora)
print("Newly added LoRA A weight:")
print(lora_A_weight)
print("Newly added LoRA B weight:")
print(lora_B_weight)


# 5. 模拟训练步骤
print("\n--- Simulating a Training Step ---")
# 准备数据和优化器
dummy_input = torch.randn(4, 10) # batch_size=4, input_dim=10
dummy_target = torch.randn(4, 5)  # batch_size=4, output_dim=5
optimizer = torch.optim.AdamW(model_with_lora.parameters(), lr=1e-3) # 优化器会自动处理可训练参数
criterion = nn.MSELoss()

# 训练前 LoRA B 的权重 (用于比较)
lora_B_before_step = model_with_lora.linear.lora_B.data.clone()

# 前向传播、计算损失、反向传播、优化
model_with_lora.train()
optimizer.zero_grad()
output = model_with_lora(dummy_input)
loss = criterion(output, dummy_target)
loss.backward()
optimizer.step()

print(f"Loss after one step: {loss.item()}")

# 6. 检查权重变化
print("\n--- Checking Weights After One Training Step ---")
original_weight_after_step = model_with_lora.linear.linear.weight.data
lora_A_after_step = model_with_lora.linear.lora_A.data
lora_B_after_step = model_with_lora.linear.lora_B.data

print("Original Linear Weight (should still be unchanged):")
print(original_weight_after_step)
print("LoRA A weight (should have changed):")
print(lora_A_after_step)
print("LoRA B weight (compare with before step):")
print("Before step:\n", lora_B_before_step)
print("After step:\n", lora_B_after_step)

# 验证原始权重是否真的没变
weight_diff = torch.sum(torch.abs(original_weight_after_step - original_weight_before_lora))
print(f"\nDifference in original weight: {weight_diff.item()} (should be 0.0)")

# 验证 LoRA B 权重是否改变
lora_B_diff = torch.sum(torch.abs(lora_B_after_step - lora_B_before_step))
print(f"Difference in LoRA B weight: {lora_B_diff.item()} (should be > 0.0)")
