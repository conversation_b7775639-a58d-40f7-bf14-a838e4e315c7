## 正态分布 (Normal Distribution)

正态分布，也称为高斯分布 (Gaussian Distribution)，是统计学和概率论中最重要的一种连续概率分布。

**特点:**
*   其概率密度函数曲线呈钟形，关于均值对称。
*   分布由两个参数决定：均值 (μ) 和标准差 (σ)。
    *   均值决定了分布的中心位置。
    *   标准差决定了分布的离散程度（曲线的胖瘦）。

**概率密度函数 (PDF):**
其数学公式为：
$$ f(x | \mu, \sigma^2) = \frac{1}{\sqrt{2\pi\sigma^2}} e^{ - \frac{(x-\mu)^2}{2\sigma^2} } $$
其中，$x$ 是变量，$\mu$ 是均值，$\sigma$ 是标准差，$\sigma^2$ 是方差。

**重要性:**
许多自然现象和社会现象的数据分布近似于正态分布。在机器学习和深度学习，尤其是在像 DDPM 这样的生成模型中，正态分布扮演着核心角色，常用于表示噪声或数据的先验分布。

---

## 前向过程 (Forward Process / Diffusion Process)

前向过程是指逐步向原始数据（例如一张清晰的图像 $x_0$）添加高斯噪声，直到数据最终变成纯粹的高斯噪声 $x_T$。这个过程被定义为一个马尔可夫链 (Markov Chain)。

**过程描述:**

在每个时间步 $t$ (从 $t=1$ 到 $t=T$)，我们向 $x_{t-1}$ 添加少量的高斯噪声，生成 $x_t$。添加的噪声量由一个预先设定的方差 $\beta_t$ 控制。

$$ q(x_t | x_{t-1}) = \mathcal{N}(x_t; \sqrt{1 - \beta_t} x_{t-1}, \beta_t I) $$

*   $q(x_t | x_{t-1})$ 表示给定 $x_{t-1}$ 时 $x_t$ 的条件概率分布。
*   $\mathcal{N}$ 代表正态分布。
*   $\sqrt{1 - \beta_t} x_{t-1}$ 是均值，表示 $x_t$ 是由 $x_{t-1}$ 缩放得到的。
*   $\beta_t I$ 是方差（$I$ 是单位矩阵），表示添加的高斯噪声的强度。$\beta_t$ 通常是一个较小的值，并且会随着 $t$ 的增大而变化（例如，从 $10^{-4}$ 逐渐增大到 $0.02$）。
*   随着 $t$ 的增加，$\beta_t$ 的累积效应使得数据逐渐失去原始信息，最终趋向于一个标准正态分布 $\mathcal{N}(0, I)$。

**任意时刻 $t$ 的 $x_t$ 的采样:**

利用正态分布的可加性，我们可以推导出从原始数据 $x_0$ 直接采样得到任意时刻 $t$ 的 $x_t$ 的公式，而不需要一步步迭代：

令 $\alpha_t = 1 - \beta_t$ 和 $\bar{\alpha}_t = \prod_{i=1}^{t} \alpha_i$。则：

$$ q(x_t | x_0) = \mathcal{N}(x_t; \sqrt{\bar{\alpha}_t} x_0, (1 - \bar{\alpha}_t) I) $$

这个公式非常重要，因为它允许我们在训练时高效地采样任意时刻 $t$ 的噪声样本 $x_t$。

**总结:**
前向过程是一个固定的、不可学习的过程，它将数据逐渐破坏为噪声。它的主要目的是为后续的反向去噪过程提供目标数据对 $(x_t, x_0)$。

## 反向过程 (Reverse Process / Denoising Process)

反向过程的目标是学习前向过程的逆过程：从纯噪声 $x_T \sim \mathcal{N}(0, I)$ 开始，逐步去除噪声，最终恢复出原始数据 $x_0$。这个过程也是一个马尔可夫链。

**挑战:**
直接计算 $q(x_{t-1} | x_t)$ 是困难的，因为它需要知道整个数据集的分布。

**解决方案:**
DDPM 使用一个神经网络（通常是 U-Net 架构）来近似这个逆向的条件概率分布 $p_\theta(x_{t-1} | x_t)$。

$$ p_\theta(x_{t-1} | x_t) = \mathcal{N}(x_{t-1}; \mu_\theta(x_t, t), \Sigma_\theta(x_t, t)) $$

*   $\mu_\theta(x_t, t)$ 是神经网络预测的均值。
*   $\Sigma_\theta(x_t, t)$ 是神经网络预测的方差。

**简化:**
论文 [Denoising Diffusion Probabilistic Models](https://arxiv.org/abs/2006.11239) 表明，可以将方差 $\Sigma_\theta(x_t, t)$ 固定为一个与 $\beta_t$ 相关的定值（例如 $\sigma_t^2 I$，其中 $\sigma_t^2 = \beta_t$ 或 $\sigma_t^2 = \tilde{\beta}_t = \frac{1 - \bar{\alpha}_{t-1}}{1 - \bar{\alpha}_t} \beta_t$），这样神经网络只需要预测均值 $\mu_\theta(x_t, t)$。

进一步地，神经网络被训练来预测在时间步 $t$ 添加到 $x_0$ 上的噪声 $\epsilon$，而不是直接预测均值 $\mu_\theta(x_t, t)$。即，训练一个模型 $\epsilon_\theta(x_t, t)$ 来近似真实的噪声 $\epsilon$。

给定预测的噪声 $\epsilon_\theta(x_t, t)$，可以推导出预测的均值：

$$ \mu_\theta(x_t, t) = \frac{1}{\sqrt{\alpha_t}} \left( x_t - \frac{\beta_t}{\sqrt{1 - \bar{\alpha}_t}} \epsilon_\theta(x_t, t) \right) $$

**训练目标:**
神经网络 $\epsilon_\theta$ 通过最小化预测噪声与真实噪声之间的差异来训练。常用的损失函数是均方误差 (MSE)：

$$ L(\theta) = \mathbb{E}_{t, x_0, \epsilon} \left[ || \epsilon - \epsilon_\theta(\sqrt{\bar{\alpha}_t} x_0 + \sqrt{1 - \bar{\alpha}_t} \epsilon, t) ||^2 \right] $$

*   $t$ 从 $\{1, ..., T\}$ 中均匀采样。
*   $x_0$ 从真实数据分布中采样。
*   $\epsilon$ 从标准正态分布 $\mathcal{N}(0, I)$ 中采样。
*   $x_t = \sqrt{\bar{\alpha}_t} x_0 + \sqrt{1 - \bar{\alpha}_t} \epsilon$ 是根据前向过程公式得到的带噪样本。

**生成过程 (Sampling):**
训练完成后，可以通过从 $x_T \sim \mathcal{N}(0, I)$ 开始，迭代地使用学习到的 $p_\theta(x_{t-1} | x_t)$ 进行采样，逐步去噪，最终生成样本 $x_0$：

For $t = T, ..., 1$:
1.  从 $\mathcal{N}(0, I)$ 中采样 $z$ (如果 $t > 1$)，否则 $z=0$。
2.  计算 $x_{t-1} = \frac{1}{\sqrt{\alpha_t}} \left( x_t - \frac{\beta_t}{\sqrt{1 - \bar{\alpha}_t}} \epsilon_\theta(x_t, t) \right) + \sigma_t z$

最终得到的 $x_0$ 就是生成的样本。

**总结:**
反向过程是 DDPM 的核心，通过训练神经网络来学习逐步去除噪声，从而实现从随机噪声生成高质量数据的目标。

---

