<mxfile host="app.diagrams.net" modified="2025-04-24T13:22:00.000Z" agent="Cascade" etag="unique_etag" version="24.2.5" type="device">
  <diagram name="ONNX Export-Import Flow" id="diagram_id_1">
    <mxGraphModel dx="1434" dy="786" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Export Flow Title -->
        <mxCell id="title_export" value="ONNX 模型导出流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="20" width="300" height="30" as="geometry" />
        </mxCell>

        <!-- Export Steps -->
        <mxCell id="export_step1" value="1. 准备框架模型&lt;br&gt;(e.g., PyTorch, TF/Keras)" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="80" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export_step2" value="2. 准备样本输入数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="260" y="80" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export_step3" value="3. 调用导出函数&lt;br&gt;(e.g., torch.onnx.export(), tf2onnx)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="80" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export_step4" value="4. 生成 .onnx 文件" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="360" y="200" width="100" height="90" as="geometry" />
        </mxCell>
        <mxCell id="export_step5" value="5. 验证模型 (可选)" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
           <mxGeometry x="520" y="210" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- Export Arrows -->
        <mxCell id="arrow_e1_3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="export_step1" target="export_step3">
           <mxGeometry relative="1" as="geometry">
             <mxPoint x="230" y="115" as="sourcePoint"/>
             <mxPoint x="480" y="115" as="targetPoint"/>
             <Array as="points">
                <mxPoint x="470" y="115"/>
                <mxPoint x="470" y="115"/>
             </Array>
           </mxGeometry>
        </mxCell>
        <mxCell id="arrow_e2_3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="export_step2" target="export_step3">
           <mxGeometry relative="1" as="geometry">
             <mxPoint x="450" y="115" as="sourcePoint"/>
             <mxPoint x="480" y="115" as="targetPoint"/>
           </mxGeometry>
        </mxCell>
         <mxCell id="arrow_e3_4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="export_step3" target="export_step4">
           <mxGeometry relative="1" as="geometry">
             <mxPoint x="570" y="150" as="sourcePoint"/>
             <mxPoint x="410" y="200" as="targetPoint"/>
             <Array as="points">
                <mxPoint x="570" y="180"/>
                <mxPoint x="410" y="180"/>
             </Array>
           </mxGeometry>
         </mxCell>
         <mxCell id="arrow_e4_5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="export_step4" target="export_step5">
           <mxGeometry relative="1" as="geometry">
             <mxPoint x="460" y="245" as="sourcePoint"/>
             <mxPoint x="520" y="245" as="targetPoint"/>
           </mxGeometry>
         </mxCell>

        <!-- Import Flow Title -->
        <mxCell id="title_import" value="ONNX 模型导入与推理流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="340" width="300" height="30" as="geometry" />
        </mxCell>

        <!-- Import Steps -->
         <mxCell id="import_step1" value="1. 加载 .onnx 文件" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="100" height="90" as="geometry" />
        </mxCell>
        <mxCell id="import_step2" value="2. 选择/创建推理引擎&lt;br&gt;(e.g., ONNX Runtime)" style="shape=component;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
           <mxGeometry x="180" y="410" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="import_step3" value="3. 创建推理会话&lt;br&gt;(ort.InferenceSession)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
           <mxGeometry x="380" y="410" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="import_step4" value="4. 准备输入数据&lt;br&gt;(e.g., NumPy Array)" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="580" y="410" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="import_step5" value="5. 执行推理&lt;br&gt;(session.run())" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="520" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="import_step6" value="6. 获取原始输出" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="280" y="520" width="140" height="70" as="geometry" />
        </mxCell>
         <mxCell id="import_step7" value="7. 后处理 (可选)" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
           <mxGeometry x="100" y="520" width="120" height="70" as="geometry" />
         </mxCell>
        <mxCell id="import_step8" value="8. 最终结果" style="shape=document;perimeter=documentPerimeter;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="100" y="630" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Import Arrows -->
         <mxCell id="arrow_i1_2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step1" target="import_step2">
            <mxGeometry relative="1" as="geometry"/>
         </mxCell>
         <mxCell id="arrow_i2_3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step2" target="import_step3">
            <mxGeometry relative="1" as="geometry"/>
         </mxCell>
        <mxCell id="arrow_i3_5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step3" target="import_step5">
           <mxGeometry relative="1" as="geometry">
              <mxPoint x="540" y="480" as="sourcePoint"/>
              <mxPoint x="480" y="555" as="targetPoint"/>
              <Array as="points">
                 <mxPoint x="510" y="480"/>
                 <mxPoint x="510" y="555"/>
              </Array>
           </mxGeometry>
        </mxCell>
         <mxCell id="arrow_i4_5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step4" target="import_step5">
           <mxGeometry relative="1" as="geometry">
              <mxPoint x="580" y="445" as="sourcePoint"/>
              <mxPoint x="620" y="555" as="targetPoint"/>
               <Array as="points">
                 <mxPoint x="650" y="480"/>
                 <mxPoint x="650" y="555"/>
              </Array>
           </mxGeometry>
         </mxCell>
         <mxCell id="arrow_i5_6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step5" target="import_step6">
            <mxGeometry relative="1" as="geometry"/>
         </mxCell>
        <mxCell id="arrow_i6_7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="import_step6" target="import_step7">
            <mxGeometry relative="1" as="geometry"/>
         </mxCell>
         <mxCell id="arrow_i7_8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="import_step7" target="import_step8">
            <mxGeometry relative="1" as="geometry"/>
         </mxCell>
         <!-- Direct arrow if no post-processing -->
         <mxCell id="arrow_i6_8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="import_step6" target="import_step8">
           <mxGeometry relative="1" as="geometry">
             <mxPoint x="350" y="590" as="sourcePoint"/>
             <mxPoint x="160" y="630" as="targetPoint"/>
             <Array as="points">
                <mxPoint x="350" y="610"/>
                <mxPoint x="160" y="610"/>
             </Array>
           </mxGeometry>
           <mxPoint as="offset"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>